﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectAttackNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectAttackNotify;
    }

    [Packable(Id = (int)MessageId.ObjectAttackNotify)]
    public class ObjectAttackNotifyPackable : MessagePackable<ObjectAttackNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectAttackNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectAttackNotify message)
        {
            message = new ObjectAttackNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}