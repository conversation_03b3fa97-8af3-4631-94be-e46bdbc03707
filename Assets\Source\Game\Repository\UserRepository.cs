using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using CLMM.Packet;
using R3;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM.Repository
{
    [Component(Scope = Scope.InstancePerLifetimeScope)]
    public class UserRepository : IRepository
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(UserRepository));

        private readonly IConnector _connector;

        private ISession _session;
        private string _account;
        private string _password;

        public List<ReactiveProperty<SelCharInfo>> SelCharInfos { get; } = new();
        public ReactiveProperty<(int code, string message)> LoginState { get; } = new();
        public ReactiveProperty<(int code, string message)> StartGameState { get; } = new();
        public ReactiveProperty<SelCharInfo> CurrentSelCharInfo { get; } = new();

        public UserRepository(IConnector connector)
        {
            _connector = connector;

            for (var i = 0; i < 4; i++)
                SelCharInfos.Add(new ReactiveProperty<SelCharInfo>());
        }

        public void Dispose() { }

        public void Login(string account, string password)
        {
            LoginState.Value = (1, "Connecting to server...");

            _account = account;
            _password = password;

            var host = IPAddress.Parse("127.0.0.1");
            var port = 7000;
            _connector.ConnectAsync(new IPEndPoint(host, port))
                .ContinueWith(task =>
                {
                    if (Logger.IsInfoEnabled)
                        Logger.Info($"{nameof(Login)}: Failed to connect to server.\r\nException - {task.Exception}");

                    LoginState.Value = (-1, "Failed to connect to server.");
                }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public void OnConnected(ISession session)
        {
            _session = session;

            LoginState.Value = (2, "Requesting client version to server...");

            var request = new ClientVersionRequest();
            request.VersionHash = ClientVersion.Hash;

            session.SendAsync(request)
                .ContinueWith(task =>
                {
                    if (Logger.IsInfoEnabled)
                        Logger.Info($"{nameof(OnConnected)}: Error sending ClientVersionRequest.\r\nException - {task.Exception}");

                    LoginState.Value = (-3, $"{nameof(OnConnected)}: Error sending ClientVersionRequest.");
                }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public void OnDisconnect(byte reason)
        {
            if (_session != null)
            {
                if (_session.IsConnected)
                    _session.CloseAsync().Continue();
                _session = null;
            }

            LoginState.Value = (-1, "Disconnected from server.");
        }

        public void OnClientVersion(byte result)
        {
            if (result != 1)
            {
                if (Logger.IsWarnEnabled)
                    Logger.Warn($"{nameof(OnClientVersion)}: Info - result: {result}");

                LoginState.Value = (-3, "Failed to negotiate client version.");
                return;
            }

            LoginState.Value = (3, "Requesting login to server ...");

            var request = new LoginRequest
            {
                Account = _account,
                Password = _password
            };

            _session.SendAsync(request)
                .ContinueWith(task =>
                {
                    if (Logger.IsInfoEnabled)
                        Logger.Info($"{nameof(OnClientVersion)}: Error sending LoginRequest.\r\nException - {task.Exception}");

                    LoginState.Value = (-4, "Error sending LoginRequest.");
                }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public void OnLogin(byte result)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(OnLogin)}: Info - result: {result}");

            LoginState.Value = (-4, $"Failed to login to server. Result: {result}");
        }

        public void OnLoginBanned(string reason, DateTime expiry)
        {
            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(OnLoginBanned)}: Info - reason: {reason} {expiry}");

            LoginState.Value = (-5, $"Failed to login to server. Reason: {reason} Expiry: {expiry}");
        }

        public void OnLoginSuccess(List<SelCharInfo> infos)
        {
            LoginState.Value = (4, "Login success.");

            for (var i = 0; i < SelCharInfos.Count; i++)
            {
                if (i < infos.Count)
                    SelCharInfos[i].Value = infos[i];
                else
                    SelCharInfos[i].Value = default;
            }

            GameBehavior.Instance.ChangeScene("LobbyScene");
        }

        public void StartGame()
        {
            StartGameState.Value = (1, "Requesting start game to server...");

            var request = new StartGameRequest
            {
                SelCharIndex = CurrentSelCharInfo.Value.Index
            };

            _session.SendAsync(request)
                .ContinueWith(task =>
                {
                    if (Logger.IsInfoEnabled)
                        Logger.Info($"{nameof(OnStartGame)}: Error sending StartGameRequest.\r\nException - {task.Exception}");

                    StartGameState.Value = (-2, "Error sending StartGameRequest.");
                }, TaskContinuationOptions.OnlyOnFaulted);
        }

        public void OnStartGame(byte result)
        {
            if (result != 4)
            {
                if (Logger.IsWarnEnabled)
                    Logger.Warn($"{nameof(OnStartGame)}: Info - result: {result}");

                StartGameState.Value = (-2, $"Failed to start game. Result: {result}");
                return;
            }

            StartGameState.Value = (2, "Starting game...");

            GameBehavior.Instance.ChangeScene("WorldScene");
        }

        public void SetCurrentSelCharInfo(int index)
        {
            var info = SelCharInfos.Find(
                info => { return info.Value.Index == index; });

            CurrentSelCharInfo.Value = info.Value;
        }
    }
}