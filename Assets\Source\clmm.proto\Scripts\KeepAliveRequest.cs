﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.TRACE)]
    public struct KeepAliveRequest : IMessage
    {
        public int Id => (ushort)MessageId.KeepAliveRequest;

        public long Time;
    }

    [Packable(Type = typeof(KeepAliveRequest))]
    public class KeepAliveRequestPackable : MessagePackable<KeepAliveRequest>
    {
        protected override void Serialize(IByteBuffer buf, KeepAliveRequest packet)
        {
            buf.WriteLongLE(packet.Time);
        }

        protected override void Deserialize(IByteBuffer buf, out KeepAliveRequest message)
        {
            throw new NotSupportedException();
        }
    }
}