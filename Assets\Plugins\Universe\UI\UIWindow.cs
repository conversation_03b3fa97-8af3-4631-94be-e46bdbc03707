﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace Universe
{
    public sealed class UIWindow : UIElement, IUIWindow
    {
        public override IUIElement Parent =>
            throw new InvalidOperationException("UIWindow cannot get parent");

        public override ICollection<IDisposable> Disposables =>
            throw new InvalidOperationException("UIWindow cannot get disposables");

        public UIWindow()
            : base(nameof(UIWindow), null)
        {
            _transform = GameObject.FindWithTag("UIRoot").transform;
        }

        public override void SetParent(IUIElement parent, Transform parentTransform = null)
        {
            throw new InvalidOperationException("UIWindow cannot set parent");
        }

        protected override void Dispose(bool disposing)
        {
            throw new InvalidOperationException("UIWindow cannot be disposed");
        }
    }
}