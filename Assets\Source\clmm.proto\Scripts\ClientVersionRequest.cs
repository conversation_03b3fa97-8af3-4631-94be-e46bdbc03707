﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ClientVersionRequest : IMessage
    {
        public int Id => (ushort)MessageId.ClientVersionRequest;

        public byte[] VersionHash;
    }

    [Packable(Type = typeof(ClientVersionRequest))]
    public class ClientVersionRequestPackable : MessagePackable<ClientVersionRequest>
    {
        protected override void Serialize(IByteBuffer buf, ClientVersionRequest packet)
        {
            buf.WriteIntLE(packet.VersionHash.Length);
            buf.WriteBytes(packet.VersionHash);
        }

        protected override void Deserialize(IByteBuffer buf, out ClientVersionRequest message)
        {
            throw new NotSupportedException();
        }
    }
}