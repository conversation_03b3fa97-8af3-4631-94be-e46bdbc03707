﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectWalkNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectWalkNotify;
    }

    [Packable(Id = (int)MessageId.ObjectWalkNotify)]
    public class ObjectWalkNotifyPackable : MessagePackable<ObjectWalkNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectWalkNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectWalkNotify message)
        {
            message = new ObjectWalkNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}