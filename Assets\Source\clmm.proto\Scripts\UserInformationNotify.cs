﻿using System;
using System.Collections.Generic;
using System.Text;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class UserInformationNotify : IMessage
    {
        public int Id => (ushort)MessageId.UserInformationNotify;

        // 基本身份信息
        public uint ObjectId; // 对象ID
        public uint RealId; // 真实ID (玩家数据库索引)
        public string Name; // 玩家名称
        public string GuildName; // 公会名称
        public string GuildRank; // 公会等级
        public uint NameColour; // 名称颜色ARGB

        // 角色属性
        public byte Class; // 职业
        public byte Gender; // 性别  
        public ushort Level; // 等级
        public int LocationX, LocationY; // X坐标,Y坐标
        public byte Direction; // 面向方向
        public byte Hair; // 发型
        public int HP; // 当前血量
        public int MP; // 当前魔法值
        public long Experience, MaxExperience; // 当前经验值,最大经验值

        public ushort LevelEffects;

        public bool HasHero; // 是否有英雄
        public byte HeroBehaviour; // 英雄行为
        public byte InventoryCount; // 背包物品数量
        public byte EquipmentCount; // 装备数量
        public byte QuestInventoryCount; // 任务物品数量

        // 货币
        public uint Gold; // 金币
        public uint Credit; // 点数

        public bool HasExpandedStorage; // 是否有扩展仓库
        public long ExpandedStorageExpiryTime; // 扩展仓库过期时间

        public byte MagicsCount; // 技能数量

        public byte IntelligentCreaturesCount; // 智能生物数量
        public byte SummonedCreatureType;
        public bool CreatureSummoned;

        public bool AllowObserve; // 允许观察
        public bool Observer; // 是否为观察者
    }

    [Packable(Id = (int)MessageId.UserInformationNotify)]
    public class UserInformationNotifyPackable : MessagePackable<UserInformationNotify>
    {
        protected override void Serialize(IByteBuffer buf, UserInformationNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out UserInformationNotify message)
        {
            message = new UserInformationNotify();

            // 基本身份信息
            message.ObjectId = buf.ReadUnsignedIntLE();
            message.RealId = buf.ReadUnsignedIntLE();
            message.Name = buf.ReadFixedString(Encoding.UTF8);
            message.GuildName = buf.ReadFixedString(Encoding.UTF8);
            message.GuildRank = buf.ReadFixedString(Encoding.UTF8);
            message.NameColour = buf.ReadUnsignedIntLE();

            // 角色属性
            message.Class = buf.ReadByte();
            message.Gender = buf.ReadByte();
            message.Level = buf.ReadUnsignedShortLE();
            message.LocationX = buf.ReadIntLE();
            message.LocationY = buf.ReadIntLE();
            message.Direction = buf.ReadByte();
            message.Hair = buf.ReadByte();
            message.HP = buf.ReadIntLE();
            message.MP = buf.ReadIntLE();
            message.Experience = buf.ReadLongLE();
            message.MaxExperience = buf.ReadLongLE();
            message.LevelEffects = buf.ReadUnsignedShortLE();
            message.HasHero = buf.ReadBoolean();
            message.HeroBehaviour = buf.ReadByte();

            /*var hasInventory = buf.ReadBoolean();
            if (hasInventory)
            {
                var count = buf.ReadByte();
                for (var i = 0; i < count; i++)
                {
                    var hasItem = buf.ReadBoolean();
                    if (!hasItem)
                        continue;
                }
            }

            var hasEquipment = buf.ReadBoolean();
            if (hasEquipment)
            {
                var count = buf.ReadByte();
                for (var i = 0; i < count; i++)
                {
                    var hasItem = buf.ReadBoolean();
                    if (!hasItem)
                        continue;
                }
            }

            var hasQuestInventory = buf.ReadBoolean();
            if (hasQuestInventory)
            {
                var count = buf.ReadByte();
                for (var i = 0; i < count; i++)
                {
                    var hasItem = buf.ReadBoolean();
                    if (!hasItem)
                        continue;
                }
            }

            // 货币
            message.Gold = buf.ReadUnsignedIntLE();
            message.Credit = buf.ReadUnsignedIntLE();

            // 状态标志
            message.HasExpandedStorage = buf.ReadBoolean();
            message.ExpandedStorageExpiryTime = buf.ReadLongLE();

            var magicsCount = buf.ReadUnsignedIntLE();
            for (var i = 0; i < magicsCount; i++) { }

            var intelligentCreaturesCount = buf.ReadUnsignedIntLE();
            for (var i = 0; i < intelligentCreaturesCount; i++) { }
            message.SummonedCreatureType = buf.ReadByte();
            message.CreatureSummoned = buf.ReadBoolean();

            // 观察者模式
            message.AllowObserve = buf.ReadBoolean();
            message.Observer = buf.ReadBoolean();*/

            // 跳过剩余的复杂数据结构 (装备、技能等详细信息)
            // 这些需要根据实际的数据结构定义来解析
            if (buf.ReadableBytes > 0)
            {
                buf.SkipBytes(buf.ReadableBytes);
            }
        }
    }
}