﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectNpcNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectNpcNotify;
    }

    [Packable(Id = (int)MessageId.ObjectNpcNotify)]
    public class ObjectNpcNotifyPackable : MessagePackable<ObjectNpcNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectNpcNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectNpcNotify message)
        {
            message = new ObjectNpcNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}