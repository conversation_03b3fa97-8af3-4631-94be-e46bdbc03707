﻿using DotNetty.Buffers;

namespace Universe
{
    public interface IMessagePackable
    {
        void Serialize(IByteBuffer buf, IMessage message);
        IMessage Deserialize(IByteBuffer buf);
    }

    public abstract class MessagePackable<T> : IMessagePackable where T : IMessage
    {
        protected abstract void Serialize(IByteBuffer buf, T packet);

        public void Serialize(IByteBuffer buf, IMessage message)
        {
            Serialize(buf, (T)message);
        }

        protected abstract void Deserialize(IByteBuffer buf, out T message);

        public IMessage Deserialize(IByteBuffer buf)
        {
            Deserialize(buf, out var packet);
            return packet;
        }
    }
}