﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectStruckNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectStruckNotify;
    }

    [Packable(Id = (int)MessageId.ObjectStruckNotify)]
    public class ObjectStruckNotifyPackable : MessagePackable<ObjectStruckNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectStruckNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectStruckNotify message)
        {
            message = new ObjectStruckNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}