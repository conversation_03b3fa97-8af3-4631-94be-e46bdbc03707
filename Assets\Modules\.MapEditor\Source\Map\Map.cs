﻿using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace CLMM
{
    public class Map
    {
        // Cell大小
        private const int CellSize = 64;

        private readonly Grid _grid;
        private readonly Dictionary<int, Sprite> _sprites;
        private readonly Dictionary<int, TileBase> _tiles;

        public Map(Grid grid, Dictionary<int, TileBase> tiles, Dictionary<int, Sprite> sprites, MapInfo info)
        {
            _grid = grid;
            _tiles = tiles;
            _sprites = sprites;
            Info = info;

            var tileSize = _grid.cellSize;
            var offsetX = -Info.Width * (int)tileSize.x / 2;
            var offsetY = Info.Height * (int)tileSize.y / 2;
            _grid.transform.localPosition = new Vector3(offsetX, offsetY, 0);

            var numCellX = (int)((float)Info.Width / CellSize + 0.5f);
            var numCellY = (int)((float)Info.Height / CellSize + 0.5f);
            Cells = new Cell[numCellX, numCellY];
        }

        public MapInfo Info { get; }
        public Cell[,] Cells { get; }

        public void LoadCell(int x, int y)
        {
            var cell = Cells[x, y];
            if (cell == null)
            {
                cell = new Cell(_grid, _tiles, _sprites, Info, x, y, CellSize);
                Cells[x, y] = cell;
            }

            cell.Load();
        }

        public void UnloadCell(int x, int y)
        {
            var cell = Cells[x, y];
            if (cell == null)
                return;
            cell.Unload();
            Cells[x, y] = null;
        }
    }
}