﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class DamageIndicatorNotify : IMessage
    {
        public int Id => (ushort)MessageId.DamageIndicatorNotify;
    }

    [Packable(Id = (int)MessageId.DamageIndicatorNotify)]
    public class DamageIndicatorNotifyPackable : MessagePackable<DamageIndicatorNotify>
    {
        protected override void Serialize(IByteBuffer buf, DamageIndicatorNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out DamageIndicatorNotify message)
        {
            message = new DamageIndicatorNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}