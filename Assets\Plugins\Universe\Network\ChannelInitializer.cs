﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Autofac;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Handlers.Logging;
using DotNetty.Handlers.Timeout;
using DotNetty.Transport.Channels;

namespace Universe
{
    public class ChannelInitializer : ChannelInitializer<TcpSession>
    {
        private readonly IProtocolSerializer _serializer;
        private readonly IMessageDispatcher _dispatcher;
        private readonly IMessageFilter _filter;

        public ChannelInitializer(ILifetimeScope container, IProtocolSerializer serializer,
            IMessageDispatcher dispatcher, IMessageFilter filter)
        {
            _serializer = serializer;
            _dispatcher = dispatcher;
            _filter = filter;
        }

        protected override void InitChannel(TcpSession channel)
        {
            channel.Initialize(_filter);
            channel.Pipeline
                .AddLast(new LoggingHandler())
                .AddLast("Timeout", new IdleStateHandler(0, 5, 0))
                .AddLast(new LengthFieldBasedFrameDecoder(ByteOrder.<PERSON><PERSON><PERSON><PERSON>, 64 * 1024, 0, 2, -2, 2, true))
                .AddLast(new ProtocolEncoder(_serializer))
                .AddLast(new ProtocolDecoder(_serializer))
                .AddLast(new SessionHandler(_dispatcher, _filter));
        }
    }
}