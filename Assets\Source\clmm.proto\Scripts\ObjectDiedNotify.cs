﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectDiedNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectDiedNotify;
    }

    [Packable(Id = (int)MessageId.ObjectDiedNotify)]
    public class ObjectDiedNotifyPackable : MessagePackable<ObjectDiedNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectDiedNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectDiedNotify message)
        {
            message = new ObjectDiedNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}