﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class NpcUpdateNotify : IMessage
    {
        public int Id => (ushort)MessageId.NpcUpdateNotify;
    }

    [Packable(Id = (int)MessageId.NpcUpdateNotify)]
    public class NpcUpdateNotifyPackable : MessagePackable<NpcUpdateNotify>
    {
        protected override void Serialize(IByteBuffer buf, NpcUpdateNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out NpcUpdateNotify message)
        {
            message = new NpcUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}