﻿using Autofac;
using CLMM.Constant;
using slf4net;
using Universe;

namespace CLMM.Scene
{
    [Component(typeof(IScene), "LoginScene")]
    public class LoginScene : BaseScene
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(LoginScene));

        public override int Tag => (int)GroupId.Login;
        protected override string[] LoadUIViews => new[] { "LoginUI" };
        protected override string[] UnloadUIViews => new[] { "LoginUI" };

        public LoginScene(ILifetimeScope container) : base(container) { }

        public override void Activate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Activating.");

            base.Activate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Activated.");
        }

        public override void Deactivate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivating");

            base.Deactivate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivated");
        }
    }
}