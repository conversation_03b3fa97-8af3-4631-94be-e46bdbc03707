﻿using UnityEngine;

namespace CLMM
{
    [RequireComponent(typeof(SpriteRenderer))]
    public class SpriteAnimation : MonoBehaviour
    {
        public static uint FrameDuration { get; set; } = 100;
        private static ulong _currentFrameCount;
        private static double _nextFrameTime;

        public static void Tick()
        {
            _currentFrameCount = (uint)(Time.realtimeSinceStartupAsDouble * 1000.0f) / FrameDuration;
            // if (Time.realtimeSinceStartupAsDouble < _nextFrameTime)
            //     return;
            // _nextFrameTime = Time.realtimeSinceStartupAsDouble + FrameDuration / 1000.0f;
            // _currentFrameCount++;
        }

        public Sprite[] Sprites;

        private SpriteRenderer _spriteRenderer;

        private int _currentFrameIndex;

        private void Awake()
        {
            _spriteRenderer = GetComponent<SpriteRenderer>();
        }

        private void Update()
        {
            UpdateSprite();
        }

        private void UpdateSprite()
        {
            var index = (int)(_currentFrameCount % (ulong)Sprites.Length);
            if (index == _currentFrameIndex)
                return;
            _spriteRenderer.sprite = Sprites[index];
            _currentFrameIndex = index;
        }
    }
}