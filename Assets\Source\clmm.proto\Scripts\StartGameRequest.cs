﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class StartGameRequest : IMessage
    {
        public int Id => (ushort)MessageId.StartGameRequest;

        public int SelCharIndex;
    }

    [Packable(Type = typeof(StartGameRequest))]
    public class StartGameRequestPackable : MessagePackable<StartGameRequest>
    {
        protected override void Serialize(IByteBuffer buf, StartGameRequest packet)
        {
            buf.WriteIntLE(packet.SelCharIndex);
        }

        protected override void Deserialize(IByteBuffer buf, out StartGameRequest message)
        {
            throw new NotSupportedException();
        }
    }
}