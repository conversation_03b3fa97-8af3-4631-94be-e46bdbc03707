using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using slf4net;

namespace Universe
{
    public class MessageDispatcher : IMessageDispatcher
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(MessageDispatcher));

        private readonly Dictionary<int, List<IMessageHandler>> _handlerMappings = new();

        private readonly ConcurrentQueue<(ISession session, IMessage message)> _queue = new();

        private readonly List<IMessageHandler> _handlers = new();

        public void Register(int id, IMessageHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Register)} {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
            {
                handlers = new List<IMessageHandler>();
                _handlerMappings.Add(id, handlers);
            }

            handlers.Add(handler);
        }

        public void Unregister(int id, IMessageHandler handler)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Unregister)} {id} {handler}");

            if (!_handlerMappings.TryGetValue(id, out var handlers))
                return;

            handlers.Remove(handler);
        }

        public void Dispatch(ISession session, IMessage message)
        {
            _queue.Enqueue((session, message));
        }

        public void Execute()
        {
            while (_queue.TryDequeue(out var result))
            {
                if (!_handlerMappings.TryGetValue(result.message.Id, out var handlers))
                    continue;

                _handlers.AddRange(handlers);

                foreach (var handler in _handlers)
                    handler.Handle(result.session, result.message);

                _handlers.Clear();
            }
        }

    }
}