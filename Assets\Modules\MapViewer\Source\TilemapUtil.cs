﻿/*using System;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Tilemaps;

namespace CLMM
{
    [RequireComponent(typeof(Tilemap))]
    public class TilemapUtil : MonoBehaviour
    {
        private Tilemap _tilemap;

        private void Awake()
        {
            _tilemap = GetComponent<Tilemap>();
        }

        // 清理TileMap中的Tile数据
        [Button("Clear Tiles", ButtonSizes.Medium), GUIColor(0.9f, 0, 0)]
        private void ClearTiles()
        {
            var tilemap = GetComponent<Tilemap>();
            if (tilemap == null)
                throw new Exception("Tilemap not found.");
            tilemap.ClearAllTiles();
        }
    }
}*/

