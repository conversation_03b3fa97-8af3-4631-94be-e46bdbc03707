﻿using System;
using System.Text;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class LoginBannedNotify : IMessage
    {
        public int Id => (ushort)MessageId.LoginBannedNotify;

        public string Reason;
        public DateTime Expiry;
    }

    [Packable(Id = (int)MessageId.LoginBannedNotify)]
    public class LoginBannedNotifyPackable : MessagePackable<LoginBannedNotify>
    {
        protected override void Serialize(IByteBuffer buf, LoginBannedNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out LoginBannedNotify message)
        {
            message = new LoginBannedNotify();
            message.Reason = buf.ReadFixedString(Encoding.UTF8);
            message.Expiry = DateTime.FromBinary(buf.ReadLongLE());
        }
    }
}