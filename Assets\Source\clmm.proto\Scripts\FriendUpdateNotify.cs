﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class FriendUpdateNotify : IMessage
    {
        public int Id => (ushort)MessageId.FriendUpdateNotify;
    }

    [Packable(Id = (int)MessageId.FriendUpdateNotify)]
    public class FriendUpdateNotifyPackable : MessagePackable<FriendUpdateNotify>
    {
        protected override void Serialize(IByteBuffer buf, FriendUpdateNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out FriendUpdateNotify message)
        {
            message = new FriendUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}