﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class BaseStatsInfoNotify : IMessage
    {
        public int Id => (ushort)MessageId.BaseStatsInfoNotify;
    }

    [Packable(Id = (int)MessageId.BaseStatsInfoNotify)]
    public class BaseStatsInfoNotifyPackable : MessagePackable<BaseStatsInfoNotify>
    {
        protected override void Serialize(IByteBuffer buf, BaseStatsInfoNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out BaseStatsInfoNotify message)
        {
            message = new BaseStatsInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}