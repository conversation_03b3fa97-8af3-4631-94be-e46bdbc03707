﻿using System;

namespace Universe
{
    public interface IMediator : IDisposable
    {
        IUIElement UI { get; set; }
        void OnCreate();
        void OnRegister();
        void OnUnregister();
    }

    public abstract class Mediator : IMediator
    {
        public IUIElement UI { get; set; }

        public abstract void OnCreate();

        public virtual void OnRegister()
        {
        }

        public virtual void OnUnregister()
        {
        }

        public virtual void Dispose()
        {
        }
    }
}