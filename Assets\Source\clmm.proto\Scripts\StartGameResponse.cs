﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class StartGameResponse : IMessage
    {
        public int Id => (ushort)MessageId.StartGameResponse;

        public byte Result;
        public int Resolution;
    }

    [Packable(Id = (int)MessageId.StartGameResponse)]
    public class StartGameResponsePackable : MessagePackable<StartGameResponse>
    {
        protected override void Serialize(IByteBuffer buf, StartGameResponse packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out StartGameResponse message)
        {
            message = new StartGameResponse();
            message.Result = buf.ReadByte();
            message.Resolution = buf.ReadIntLE();
        }
    }
}