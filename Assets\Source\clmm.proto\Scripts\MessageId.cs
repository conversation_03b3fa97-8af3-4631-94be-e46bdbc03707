﻿using System;

namespace CLMM.Packet
{
    public enum MessageId : ushort
    {
        ConnectedNotify = 0,
        ClientVersionRequest = 0,
        ClientVersionResponse = 1,
        DisconnectNotify = 2,
        KeepAliveRequest = 2,
        KeepAliveResponse = 3,
        LoginRequest = 5,
        LoginResponse = 7,
        LoginBannedNotify = 8,
        LoginSuccessNotify = 9,
        StartGameRequest = 8,
        StartGameResponse = 14,
        MapInformationNotify = 17,
        UserInformationNotify = 21,
        ObjectPlayerNotify = 24,
        ObjectHeroNotify = 25,
        ObjectRemoveNotify = 26,
        ObjectTurnNotify = 27,
        ObjectWalkNotify = 28,
        ObjectRunNotify = 29,
        ChannelMessageNotify = 30,
        NewItemInfoNotify = 32,
        TimeOfDayNotify = 59,
        ChangeAttackModeNotify = 60,
        ChangePetModeNotify = 61,
        ObjectMonsterNotify = 69,
        ObjectAttackNotify = 70,
        ObjectStruckNotify = 72,
        DamageIndicatorNotify = 73,
        ObjectDiedNotify = 79,
        ObjectNpcNotify = 90,
        NpcResponseNotify = 91,
        SwitchGroupNotify = 129,
        AddBuffNotify = 142,
        RemoveBuffNotify = 143,
        PauseBuffNotify = 144,
        BaseStatsInfoNotify = 160,
        DefaultNpcNotify = 184,
        NpcUpdateNotify = 185,
        CompleteQuestNotify = 200,
        NewQuestInfoNotify = 202,
        ReceiveMailNotify = 229,
        FriendUpdateNotify = 243,
        LoverUpdateNotify = 244,
        MentorUpdateNotify = 245,
        GuildBuffListNotify = 246,
        GameShopInfoNotify = 248,
        NewRecipeInfoNotify = 264,
    }
}