﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectHeroNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectHeroNotify;
    }

    [Packable(Id = (int)MessageId.ObjectHeroNotify)]
    public class ObjectHeroNotifyPackable : MessagePackable<ObjectHeroNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectHeroNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectHeroNotify message)
        {
            message = new ObjectHeroNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}