﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ChangeAttackModeNotify : IMessage
    {
        public int Id => (ushort)MessageId.ChangeAttackModeNotify;
    }

    [Packable(Id = (int)MessageId.ChangeAttackModeNotify)]
    public class ChangeAttackModeNotifyPackable : MessagePackable<ChangeAttackModeNotify>
    {
        protected override void Serialize(IByteBuffer buf, ChangeAttackModeNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ChangeAttackModeNotify message)
        {
            message = new ChangeAttackModeNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}