﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ChangePetModeNotify : IMessage
    {
        public int Id => (ushort)MessageId.ChangePetModeNotify;
    }

    [Packable(Id = (int)MessageId.ChangePetModeNotify)]
    public class ChangePModeNotifyPackable : MessagePackable<ChangePetModeNotify>
    {
        protected override void Serialize(IByteBuffer buf, ChangePetModeNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ChangePetModeNotify message)
        {
            message = new ChangePetModeNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}