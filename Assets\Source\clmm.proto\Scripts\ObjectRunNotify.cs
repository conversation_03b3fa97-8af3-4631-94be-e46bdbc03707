﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectRunNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectRunNotify;
    }

    [Packable(Id = (int)MessageId.ObjectRunNotify)]
    public class ObjectRunNotifyPackable : MessagePackable<ObjectRunNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectRunNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectRunNotify message)
        {
            message = new ObjectRunNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}