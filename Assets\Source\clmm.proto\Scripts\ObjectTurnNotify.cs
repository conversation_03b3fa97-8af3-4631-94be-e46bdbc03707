﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectTurnNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectTurnNotify;
    }

    [Packable(Id = (int)MessageId.ObjectTurnNotify)]
    public class ObjectTurnNotifyPackable : MessagePackable<ObjectTurnNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectTurnNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectTurnNotify message)
        {
            message = new ObjectTurnNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}