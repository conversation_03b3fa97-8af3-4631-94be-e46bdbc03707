﻿using System;

namespace Universe
{
    public class EventDispatcher : IEventDispatcher
    {
        public void Register(int eventId, Func<ICommand> func)
        {
            throw new NotImplementedException();
        }

        public void Unregister(int eventId, Func<ICommand> func)
        {
            throw new NotImplementedException();
        }

        public void Dispatch(int eventId, object data = null)
        {
        }
    }
}