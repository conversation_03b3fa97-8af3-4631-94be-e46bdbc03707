﻿using System;

namespace Universe
{
    public enum Scope
    {
        InstancePerDependency,
        SingleInstance,
        InstancePerLifetimeScope
    }

    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class ComponentAttribute : Attribute
    {
        public Type Service { get; }
        public string Name { get; }
        public Scope Scope { get; set; }

        public ComponentAttribute()
        {
        }

        public ComponentAttribute(Type service)
        {
            Service = service;
        }

        public ComponentAttribute(Type service, string name) : this(service)
        {
            Name = name;
        }
    }
}