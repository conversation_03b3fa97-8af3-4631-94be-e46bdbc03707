using CLMM.Constant;
using CLMM.Packet;
using CLMM.Repository;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.LoginSuccessNotify, Tag = (int)GroupId.Login)]
    public class LoginSuccessNotifyHandler : MessageHandler<LoginSuccessNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(LoginSuccessNotifyHandler));

        private readonly UserRepository _userRepository;

        public LoginSuccessNotifyHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, LoginSuccessNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: Info - {nameof(LoginSuccessNotify)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnLoginSuccess(message.SelCharInfos);
        }
    }
}