﻿using System;
using System.Text;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ChannelMessageNotify : IMessage
    {
        public int Id => (ushort)MessageId.ChannelMessageNotify;

        public string Content;
        public byte ChannelType;
    }

    [Packable(Id = (int)MessageId.ChannelMessageNotify)]
    public class ChatNotifyPackable : MessagePackable<ChannelMessageNotify>
    {
        protected override void Serialize(IByteBuffer buf, ChannelMessageNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ChannelMessageNotify message)
        {
            message = new ChannelMessageNotify();
            message.Content = buf.ReadFixedString(Encoding.UTF8);
            message.ChannelType = buf.ReadByte();
        }
    }
}