using CLMM.Constant;
using CLMM.Packet;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.ChannelMessageNotify, Tag = (int)GroupId.World)]
    public class ChannelMessageNotifyHandler : MessageHandler<ChannelMessageNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(ChannelMessageNotifyHandler));

        protected override void Handle(ISession session, ChannelMessageNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: Info - {nameof(ChannelMessageNotify)} {JsonConvert.SerializeObject(message)}");
        }
    }
}