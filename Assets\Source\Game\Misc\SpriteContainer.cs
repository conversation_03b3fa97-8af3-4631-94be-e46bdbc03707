﻿using System.Collections.Generic;

namespace UnityEngine.UI
{
    public class SpriteContainer : MonoBehaviour
    {
        public List<Sprite> Sprites = new();

        private Dictionary<string, Sprite> _dict;

        public Sprite GetSprite(string key)
        {
            if (_dict == null)
            {
                _dict = new Dictionary<string, Sprite>();
                foreach (var sprite in Sprites)
                    _dict.Add(sprite.name, sprite);
            }

            _dict.TryGetValue(key, out var value);
            return value;
        }
    }
}