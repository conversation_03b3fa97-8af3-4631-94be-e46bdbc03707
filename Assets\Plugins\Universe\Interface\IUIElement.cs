﻿using System;
using System.Collections.Generic;
using R3;
using UnityEngine;

namespace Universe
{
    public interface IUIElement : IDisposable
    {
        string Name { get; }
        object Userdata { get; }
        bool IsDisposed { get; }
        BehaviorSubject<int> StateSubject { get; }
        ICollection<IDisposable> Disposables { get; }
        Transform Transform { get; }
        IUIElement Parent { get; }
        List<IUIElement> Children { get; }
        void SetParent(IUIElement parent, Transform parentTransform = null);
    }
}