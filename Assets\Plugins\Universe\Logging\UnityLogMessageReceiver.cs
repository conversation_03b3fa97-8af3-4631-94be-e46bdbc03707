using System.Threading;
using UnityEngine;

namespace slf4net.extensions
{
    public static class UnityLogMessageReceiver
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger("Unity");

        private static void OnLogMessageReceived(string condition, string trace, LogType type)
        {
            switch (type)
            {
                case LogType.Log:
                    Logger.Info(condition + "\n" + trace);
                    break;
                case LogType.Warning:
                    Logger.Warn(condition + "\n" + trace);
                    break;
                default:
                    Logger.Error(condition + "\n" + trace);
                    break;
            }
        }

        public static void Initialize(SynchronizationContext context)
        {
            Application.logMessageReceivedThreaded += (condition, trace, type) =>
            {
#if UNITY_EDITOR
                if (condition.StartsWith("♪"))
                    return;
#endif
                context.Send(_ => OnLogMessageReceived(condition, trace, type), null);
            };
        }
    }
}