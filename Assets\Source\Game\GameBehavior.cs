﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Autofac;
using slf4net;
using UnityEngine;
using UnityEngine.AddressableAssets;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM
{
    public class GameBehavior : MonoBehaviour
    {
        private static ILogger Logger;

        private static readonly Lazy<GameBehavior> LazyInstance = new(() =>
        {
            var instance = FindFirstObjectByType<GameBehavior>();
            if (instance == null)
            {
                var go = new GameObject($"[{nameof(GameBehavior)}]");
                DontDestroyOnLoad(go);
                instance = go.AddComponent<GameBehavior>();
            }

            return instance;
        });

        public static GameBehavior Instance => LazyInstance.Value;

        private IContainer _container;

        private List<IService> _services;

        private IScene _currentScene;

        public void Initialize(IContainer container)
        {
            _container = container;

            _services = _container.Resolve<IEnumerable<IService>>().ToList();
            foreach (var service in _services)
                service.Initialize();

            NetworkSerializerRegister();
            NetworkFilterRegister();
        }

        private void Start()
        {
            Logger = LoggerFactory.GetLogger(typeof(GameBehavior));

            if (Logger.IsInfoEnabled)
                Logger.Info($"{nameof(Start)}.");

            var initializeHandle = Addressables.InitializeAsync();
            initializeHandle.WaitForCompletion();

            ChangeScene("LoginScene");
        }

        private void Update()
        {
            foreach (var service in _services)
                service.Update();

            World.Update();
        }

        private void OnDestroy()
        {
            foreach (var service in _services)
                service.Shutdown();
        }

        private void NetworkSerializerRegister()
        {
            var protocolSerializer = _container.Resolve<IProtocolSerializer>();

            var types = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(type => typeof(IMessagePackable).IsAssignableFrom(type)
                               && !type.IsAbstract
                               && !type.IsInterface)
                .ToList();

            foreach (var type in types)
                protocolSerializer.Register((IMessagePackable)Activator.CreateInstance(type));
        }

        private void NetworkFilterRegister()
        {
            var filter = _container.Resolve<IMessageFilter>();

            var types = Assembly.GetExecutingAssembly()
                .GetTypes()
                .Where(type => typeof(IMessage).IsAssignableFrom(type)
                               && !type.IsAbstract
                               && !type.IsInterface)
                .ToList();

            foreach (var type in types)
            {
                var attribute = type.GetCustomAttribute<ProtocolAttribute>();
                if (attribute == null)
                    continue;

                filter.Add(type, attribute.LogLevel);
            }
        }

        public void ChangeScene(string sceneName)
        {
            var scene = _container.ResolveNamed<IScene>(sceneName);
            if (scene == null)
                return;

            _currentScene?.Deactivate();
            _currentScene = scene;
            _currentScene?.Activate();
        }
    }
}