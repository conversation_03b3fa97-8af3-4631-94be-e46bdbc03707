using CLMM.Constant;
using CLMM.Packet;
using CLMM.Repository;
using slf4net;
using Universe;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.LoginResponse, Tag = (int)GroupId.Login)]
    public class LoginResponseHandler : MessageHandler<LoginResponse>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(LoginResponseHandler));

        private readonly UserRepository _userRepository;

        public LoginResponseHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, LoginResponse message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: Info - {nameof(LoginResponse)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnLogin(message.Result);
        }
    }
}