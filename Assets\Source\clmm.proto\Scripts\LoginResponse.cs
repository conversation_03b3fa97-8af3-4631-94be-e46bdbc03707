﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class LoginResponse : IMessage
    {
        public int Id => (ushort)MessageId.LoginResponse;

        public byte Result;
    }

    [Packable(Id = (int)MessageId.LoginResponse)]
    public class LoginResponsePackable : MessagePackable<LoginResponse>
    {
        protected override void Serialize(IByteBuffer buf, LoginResponse packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out LoginResponse message)
        {
            message = new LoginResponse();
            message.Result = buf.ReadByte();
        }
    }
}