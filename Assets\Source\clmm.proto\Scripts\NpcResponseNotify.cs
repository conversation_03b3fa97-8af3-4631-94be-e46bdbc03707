﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class NpcResponseNotify : IMessage
    {
        public int Id => (ushort)MessageId.NpcResponseNotify;
    }

    [Packable(Id = (int)MessageId.NpcResponseNotify)]
    public class NpcResponseNotifyPackable : MessagePackable<NpcResponseNotify>
    {
        protected override void Serialize(IByteBuffer buf, NpcResponseNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out NpcResponseNotify message)
        {
            message = new NpcResponseNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}