﻿using Autofac;
using UnityEngine;

namespace Universe
{
    public sealed class UIWidget : UIElement, IUIWidget
    {
        public UIWidget(ILifetimeScope container, string name, Transform transform)
            : this(container, name, transform, null)
        {
        }

        public UIWidget(ILifetimeScope container, string name, Transform transform, object userdata)
            : base(name, userdata)
        {
            _transform = transform;

            if (container.TryResolveNamed(_name, out _mediator))
                _mediator.UI = this;

            _mediator?.OnCreate();

            StateSubject.OnNext(1);
        }

        public override void SetParent(IUIElement parent, Transform parentTransform = null)
        {
            if (_parent != null)
            {
                _parent.Children.Remove(this);

                _transform.SetParent(_cacheTransform, false);
                _mediator?.OnUnregister();
            }

            _parent = parent;

            if (_parent != null)
            {
                _parent.Children.Add(this);

                if (ReferenceEquals(parentTransform, null))
                    _transform.SetParent(_parent.Transform, false);
                else if (parentTransform != _transform.parent)
                    _transform.SetParent(parentTransform, false);
                _mediator?.OnRegister();
            }
        }
    }
}