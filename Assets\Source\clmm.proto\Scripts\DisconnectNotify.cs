﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class DisconnectNotify : IMessage
    {
        public int Id => (ushort)MessageId.DisconnectNotify;

        public byte Reason;
    }

    [Packable(Id = (int)MessageId.DisconnectNotify)]
    public class DisconnectNotifyPackable : MessagePackable<DisconnectNotify>
    {
        protected override void Serialize(IByteBuffer buf, DisconnectNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out DisconnectNotify message)
        {
            message = new DisconnectNotify();
            message.Reason = buf.ReadByte();
        }
    }
}