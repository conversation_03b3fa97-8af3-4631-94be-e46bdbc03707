﻿using System;
using System.Collections.Generic;
using System.IO;
using MemoryPack;
using UnityEditor;
using UnityEngine;

namespace CLMM.MirTools
{
    public static partial class MirExport
    {
        private static readonly Dictionary<int, string> MirLibMappings = new();

        private static readonly Dictionary<string, Dictionary<int, (short, bool)>> RefSpriteAtlasMappings = new();

        private static (short, bool) GetSpriteInfo(string libName, int imageId)
        {
            if (!RefSpriteAtlasMappings.TryGetValue(libName, out var refSpriteMappings))
            {
                refSpriteMappings = new Dictionary<int, (short, bool)>();
                RefSpriteAtlasMappings.Add(libName, refSpriteMappings);

                var guids = AssetDatabase.FindAssets("t:RefSpriteAtlas", new[] { $"Assets/Content/Map/Atlas/{libName}" });

                foreach (var guid in guids)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var refSpriteAtlas = AssetDatabase.LoadAssetAtPath<RefSpriteAtlas>(assetPath);
                    var atlasIndex = short.Parse(refSpriteAtlas.Atlas.name);
                    foreach (var (id, sprite) in refSpriteAtlas.Sprites)
                    {
                        var size = sprite.rect.size;
                        var width = (int)size.x;
                        var height = (int)size.y;
                        var isTile = width == 48 && height == 32 || width == 96 && height == 64;
                        refSpriteMappings.Add(id, (atlasIndex, isTile));
                    }
                }
            }

            if (refSpriteMappings.TryGetValue(imageId, out var result))
                return result;

            return (-1, false);
        }

        [MenuItem("MitTools/Export All Map Data")]
        private static void ExportAllMapData()
        {
            EditorUtility.DisplayProgressBar("Exporting All Map Data", "Processing ...", 0);

            MirLibMappings.Clear();
            MirLibMappings.Add(0, "WemadeMir2/Tiles");
            MirLibMappings.Add(1, "WemadeMir2/SmTiles");
            MirLibMappings.Add(2, "WemadeMir2/Objects");
            for (var i = 2; i < 28; i++)
                MirLibMappings.Add(i + 1, $"WemadeMir2/Objects{i}");
            MirLibMappings.Add(90, "WemadeMir2/Objects_32bit");

            try
            {
                const string folder = "E:/Workspace/Project/Crystal/Build/Client/Debug";

                var fileMapping = new Dictionary<string, (string, int)>
                {
                    { "Map", ("Map/Data", World.ChunkSize) }
                };

                foreach (var (key, (path, chunkSize)) in fileMapping)
                {
                    var files = Directory.GetFiles(Path.Combine(folder, key), "*.map");

                    var index = 0;
                    var count = files.Length;
                    foreach (var file in files)
                    {
                        EditorUtility.DisplayProgressBar(
                            $"Generating Map({++index}/{count})",
                            "Processing " + file,
                            (float)index / count);

                        var output = $"Assets/Content/{path}";

                        GenerateMap(file, output, chunkSize);
                    }
                }
            }
            finally
            {
                RefSpriteAtlasMappings.Clear();

                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        private static void GenerateMap(string file, string output, int chunkSize)
        {
            var mirMapInfo = new MirMapInfo(file);
            if (mirMapInfo.Width <= 0 || mirMapInfo.Height <= 0)
                return;

            var chunkCountX = (int)((float)mirMapInfo.Width / chunkSize + 0.5f);
            var chunkCountY = (int)((float)mirMapInfo.Height / chunkSize + 0.5f);

            var chunks = new byte[chunkCountX, chunkCountY][];
            for (var y = 0; y < chunkCountY; y++)
            {
                for (var x = 0; x < chunkCountX; x++)
                {
                    var chunkInfo = GenerateChunk(mirMapInfo, new Vector2Int(x, y), chunkSize);
                    chunks[x, y] = MemoryPackSerializer.Serialize(chunkInfo);
                }
            }

            var mapInfo = new MapInfo();
            mapInfo.Width = mirMapInfo.Width;
            mapInfo.Height = mirMapInfo.Height;
            mapInfo.Chunks = chunks;

            var bytes = MemoryPackSerializer.Serialize(mapInfo);
            var path = $"{output}/{Path.GetFileNameWithoutExtension(file)}.map.bytes";
            var directory = Path.GetDirectoryName(path);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);
            if (File.Exists(path))
                File.Delete(path);
            File.WriteAllBytes(path, bytes);
        }

        private static (byte, int)[,] GenerateBaseLayer(MirMapInfo mapInfo, Vector2Int chunkIndex, int chunkSize, IDictionary<int, byte> paletteMappings, ICollection<string> palettes)
        {
            var xOffset = chunkIndex.x * chunkSize;
            var yOffset = chunkIndex.y * chunkSize;

            const int scale = 2;
            var size = chunkSize / scale;
            var tileIds = new (byte, int)[size, size];
            for (var cy = 0; cy < size; cy++)
            {
                for (var cx = 0; cx < size; cx++)
                {
                    tileIds[cx, cy] = (0, -1);

                    var mx = cx * scale + xOffset;
                    var my = cy * scale + yOffset;
                    if (mx >= mapInfo.Width || my >= mapInfo.Height)
                        continue;

                    var cellInfo = mapInfo.CellInfos[mx, my];
                    if (cellInfo.BackIndex < 0)
                        continue;

                    var imageId = (cellInfo.BackImage & 0x1FFFFFFF) - 1;
                    if (imageId < 0)
                        continue;

                    var libId = cellInfo.BackIndex;
                    if (!MirLibMappings.TryGetValue(libId, out var libName))
                    {
                        Debug.LogWarning($"Invalid libId: {libId}");
                        continue;
                    }

                    var (atlasIndex, _) = GetSpriteInfo(libName, imageId);
                    if (atlasIndex < 0)
                        continue;

                    var pId = libId << 16 | (ushort)atlasIndex;
                    if (!paletteMappings.TryGetValue(pId, out var pIndex))
                    {
                        pIndex = (byte)palettes.Count;
                        palettes.Add($"{libName}/{atlasIndex}");
                        paletteMappings.Add(pId, pIndex);
                    }
                    tileIds[cx, cy] = (pIndex, imageId);
                }
            }

            return tileIds;
        }

        private static (short, int) GetLayerInfo(int layer, MirCellInfo info)
        {
            switch (layer)
            {
                case 1:
                    return (info.MiddleIndex, info.MiddleImage);
                case 2:
                    return (info.FrontIndex, info.FrontImage);
            }
            throw new ArgumentOutOfRangeException();
        }

        private static (byte, byte, byte, int)[] GenerateTileLayer(int layer, MirMapInfo mapInfo, Vector2Int chunkIndex, int chunkSize, IDictionary<int, byte> paletteMappings, ICollection<string> palettes, ICollection<(byte, byte, byte, int)> objects)
        {
            var xOffset = chunkIndex.x * chunkSize;
            var yOffset = chunkIndex.y * chunkSize;

            var tiles = new List<(byte, byte, byte, int)>();
            for (var cy = 0; cy < chunkSize; cy++)
            {
                for (var cx = 0; cx < chunkSize; cx++)
                {
                    var mx = cx + xOffset;
                    var my = cy + yOffset;
                    if (mx >= mapInfo.Width || my >= mapInfo.Height)
                        continue;

                    var cellInfo = mapInfo.CellInfos[mx, my];
                    var (libId, image) = GetLayerInfo(layer + 1, cellInfo);
                    if (libId < 0)
                        continue;

                    var imageId = layer == 1
                        ? (image & 0x7FFF) - 1
                        : image - 1;

                    if (imageId < 0)
                        continue;

                    if (!MirLibMappings.TryGetValue(libId, out var libName))
                    {
                        Debug.LogWarning($"Invalid libId: {libId}");
                        continue;
                    }

                    var (atlasIndex, isTile) = GetSpriteInfo(libName, imageId);
                    if (atlasIndex < 0)
                        continue;

                    if (layer == 1)
                    {
                        var animationFrame = cellInfo.FrontAnimationFrame;
                        if ((animationFrame & 0x80) > 0)
                            animationFrame &= 0x7F;

                        if (animationFrame > 1)
                            continue;
                    }

                    var pId = libId << 16 | (ushort)atlasIndex;
                    if (!paletteMappings.TryGetValue(pId, out var pIndex))
                    {
                        pIndex = (byte)palettes.Count;
                        palettes.Add($"{libName}/{atlasIndex}");
                        paletteMappings.Add(pId, pIndex);
                    }

                    if (isTile)
                        tiles.Add(((byte)cx, (byte)cy, pIndex, imageId));
                    else
                        objects.Add(((byte)cx, (byte)cy, pIndex, imageId));
                }
            }

            return tiles.ToArray();
        }

        private static ChunkInfo GenerateChunk(MirMapInfo mapInfo, Vector2Int chunkIndex, int chunkSize)
        {
            var palettes = new List<string>();
            var paletteMappings = new Dictionary<int, byte>();

            var chunkInfo = new ChunkInfo();
            chunkInfo.Index = chunkIndex;
            chunkInfo.TileIds = GenerateBaseLayer(mapInfo, chunkIndex, chunkSize, paletteMappings, palettes);

            var objects = new List<(byte, byte, byte, int)>();
            chunkInfo.Object2Array[0] =
                GenerateTileLayer(0, mapInfo, chunkIndex, chunkSize, paletteMappings, palettes, objects);
            if (objects.Count > 0)
                throw new Exception("Layer 0 cannot have an Object");
            chunkInfo.Object2Array[1] =
                GenerateTileLayer(1, mapInfo, chunkIndex, chunkSize, paletteMappings, palettes, objects);
            chunkInfo.Object2Array[2] = objects.ToArray();
            chunkInfo.Palettes = palettes.ToArray();

            return chunkInfo;
        }
    }
}