﻿using Autofac;
using CLMM.Constant;
using slf4net;
using Universe;

namespace CLMM.Scene
{
    [Component(typeof(IScene), "LobbyScene")]
    public class LobbyScene : BaseScene
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(LobbyScene));

        public override int Tag => (int)GroupId.Lobby;
        protected override string[] LoadUIViews => new[] { "LobbyUI" };
        protected override string[] UnloadUIViews => new[] { "LobbyUI" };

        public LobbyScene(ILifetimeScope container) : base(container) { }

        public override void Activate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Activating.");

            base.Activate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Activated.");
        }

        public override void Deactivate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivating.");

            base.Deactivate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivated.");
        }
    }
}