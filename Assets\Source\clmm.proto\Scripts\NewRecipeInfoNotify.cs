﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.DEBUG)]
    public class NewRecipeInfoNotify : IMessage
    {
        public int Id => (ushort)MessageId.NewRecipeInfoNotify;
    }

    [Packable(Id = (int)MessageId.NewRecipeInfoNotify)]
    public class NewRecipeInfoNotifyPackable : MessagePackable<NewRecipeInfoNotify>
    {
        protected override void Serialize(IByteBuffer buf, NewRecipeInfoNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out NewRecipeInfoNotify message)
        {
            message = new NewRecipeInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}