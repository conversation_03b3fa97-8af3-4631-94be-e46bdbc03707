using MemoryPack;

namespace CLMM
{
    // [MemoryPackable]
    // public partial struct MapEntry
    // {
    //     // public string Name;
    //     // public int Width;
    //     // public int Height;
    //     // public int ChuckSize;
    //     // public int CellSize;
    //     // public Vector2Int NumCells;
    //     //
    //     // public CellEntry[,] Cells;
    // }

    public struct TileInfo
    {
        public short BackIndex;
        public int BackImage;
        public short MiddleIndex;
        public int MiddleImage;
        public short FrontIndex;
        public int FrontImage;

        public byte DoorIndex;
        public byte DoorOffset;

        public byte FrontAnimationFrame;
        public byte FrontAnimationTick;

        public byte MiddleAnimationFrame;
        public byte MiddleAnimationTick;

        public short TileAnimationImage;
        public short TileAnimationOffset;
        public byte TileAnimationFrames;

        public byte Light;
    }

    [MemoryPackable]
    public partial struct MapInfo
    {
        public int Width, Height;
        public TileInfo[,] TileInfos;
    }
}