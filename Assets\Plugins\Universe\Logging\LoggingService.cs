using System.IO;
using System.Threading;
using System.Xml;
using NLog;
using NLog.Config;
using slf4net;
using slf4net.extensions;
using slf4net.NLog;
using UnityEngine;

namespace Universe
{
    public class LoggingService : IService
    {
        static LoggingService()
        {
            LoggerFactory.SetServiceProviderResolver(new ManualFactoryResolver(new NLogLoggerFactory()));
        }

        public void Initialize()
        {
            ConfigurationItemFactory.Default.TargetFactory.RegisterType<UnityConsoleTarget>("UnityConsole");

            var content = string.Empty;
            if (Application.isEditor)
            {
                var path = Path.Combine(Application.streamingAssetsPath, "NLog.xml");
                var text = File.ReadAllText(path);
                content = text
                    .Replace("${basedir}", Directory.GetCurrentDirectory())
                    .Replace("${date:format=HH\\:mm\\:ss.fff}", "${date:format=fff}");
            }

            if (string.IsNullOrEmpty(content))
                throw new FileNotFoundException("NLog.xml not found");

            using var reader = XmlReader.Create(new StringReader(content));
            LogManager.Configuration = new XmlLoggingConfiguration(reader);

            UnityLogMessageReceiver.Initialize(SynchronizationContext.Current);
        }

        public void Update()
        {
        }

        public void Shutdown()
        {
        }
    }
}