﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.TRACE)]
    public struct KeepAliveResponse : IMessage
    {
        public int Id => (ushort)MessageId.KeepAliveResponse;

        public long Time;
    }

    [Packable(Id = (int)MessageId.KeepAliveResponse)]
    public class KeepAliveResponsePackable : MessagePackable<KeepAliveResponse>
    {
        protected override void Serialize(IByteBuffer buf, KeepAliveResponse packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out KeepAliveResponse message)
        {
            message = new KeepAliveResponse();
            message.Time = buf.ReadLongLE();
        }
    }
}