﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class GuildBuffListNotify : IMessage
    {
        public int Id => (ushort)MessageId.GuildBuffListNotify;
    }

    [Packable(Id = (int)MessageId.GuildBuffListNotify)]
    public class GuildBuffListNotifyPackable : MessagePackable<GuildBuffListNotify>
    {
        protected override void Serialize(IByteBuffer buf, GuildBuffListNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out GuildBuffListNotify message)
        {
            message = new GuildBuffListNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}