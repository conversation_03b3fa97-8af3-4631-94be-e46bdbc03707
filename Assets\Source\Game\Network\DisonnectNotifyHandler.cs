using CLMM.Constant;
using CLMM.Packet;
using CLMM.Repository;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.DisconnectNotify, Tag = (int)GroupId.Global)]
    public class DisconnectNotifyHandler : MessageHandler<DisconnectNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(DisconnectNotifyHandler));

        private UserRepository _userRepository;

        public DisconnectNotifyHandler(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        protected override void Handle(ISession session, DisconnectNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: Info - {nameof(DisconnectNotify)} {JsonConvert.SerializeObject(message)}");

            _userRepository.OnDisconnect(message.Reason);
        }
    }
}