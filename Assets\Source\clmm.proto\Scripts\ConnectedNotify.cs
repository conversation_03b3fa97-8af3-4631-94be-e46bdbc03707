﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ConnectedNotify : IMessage
    {
        public int Id => (ushort)MessageId.ConnectedNotify;
    }

    [Packable(Id = (int)MessageId.ConnectedNotify)]
    public class ConnectedNotifyPackable : MessagePackable<ConnectedNotify>
    {
        protected override void Serialize(IByteBuffer buf, ConnectedNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ConnectedNotify message)
        {
            message = new ConnectedNotify();
        }
    }
}