﻿// using System;
// using System.Collections.Generic;
// using MemoryPack;
// using Sirenix.OdinInspector;
// using Sirenix.Utilities;
// using UnityEngine;
// using UnityEngine.Tilemaps;
// #if UNITY_EDITOR
// using UnityEditor;
// #endif // UNITY_EDITOR
//
// namespace CLMM.MapEditor
// {
//     public class MapEditor : MonoBehaviour
//     {
//         [BoxGroup("Grid", false, Order = -2)]
//         public Grid Grid;
//
//         [BoxGroup("Map", Order = 0)]
//         [ValidateInput("ValidateMapAsset")]
//         [LabelText("Asset")]
//         public TextAsset MapAsset;
//
//         [ShowIf("_map")]
//         [BoxGroup("Map Info")]
//         [ShowInInspector]
//         [OnValueChanged("OnCellsChanged")]
//         [TableMatrix(HorizontalTitle = "Cells Size = 64", DrawElementMethod = "DrawCellElement", SquareCells = true)]
//         private bool[,] _cells;
//
//         private Map _map;
//
//         // [ShowIf("_map")]
//         // [BoxGroup("Map Info", Order = 100)]
//         // [ShowInInspector]
//         // private int _width, _height;
//
//         private bool ValidateMapAsset(TextAsset value, ref string errorMessage)
//         {
//             if (value == null)
//                 return false;
//
//             // Check if the asset is a valid map
//             if (!value.name.Contains(".map"))
//             {
//                 errorMessage = "\"" + value.name + "\" is not a valid map file";
//                 return false;
//             }
//
//             return true;
//         }
//
//         private void FixedUpdate()
//         {
//             SpriteAnimation.Tick();
//         }
//
//         [EnableIf("MapAsset")]
//         [BoxGroup("Map")]
//         [Button("LoadMap", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         public void LoadMap()
//         {
//             try
//             {
//                 var tiles = new Dictionary<int, TileBase>();
//                 var guids = AssetDatabase.FindAssets("t:TileAtlas",
//                     new[] { "Assets/Content/Map/Atlas/WemadeMir2" });
//                 foreach (var guid in guids)
//                 {
//                     var atlasPath = AssetDatabase.GUIDToAssetPath(guid);
//                     var tileAtlas = AssetDatabase.LoadAssetAtPath<TileAtlas>(atlasPath);
//                     var atlasId = tileAtlas.Id;
//                     foreach (var (tileId, tile) in tileAtlas.Tiles)
//                     {
//                         var key = (atlasId << 24) | (tileId & 0xFFFFFF);
//                         tiles.Add(key, tile);
//                     }
//                 }
//
//                 var sprites = new Dictionary<int, Sprite>();
//                 guids = AssetDatabase.FindAssets("t:SpriteAtlasReference",
//                     new[]
//                     {
//                         "Assets/Content/Map/Atlas/WemadeMir2",
//                         "Assets/Content/Map/AtlasV2/WemadeMir2"
//                     });
//                 foreach (var guid in guids)
//                 {
//                     var referencePath = AssetDatabase.GUIDToAssetPath(guid);
//                     var spriteAtlasReference = AssetDatabase.LoadAssetAtPath<SpriteAtlasReference>(referencePath);
//                     var atlasId = spriteAtlasReference.Id;
//                     foreach (var (spriteId, sprite) in spriteAtlasReference.Sprites)
//                     {
//                         var key = (atlasId << 24) | (spriteId & 0xFFFFFF);
//                         try
//                         {
//                             sprites.Add(key, sprite);
//                         }
//                         catch (Exception e)
//                         {
//                             Debug.LogError(e.Message);
//                             throw;
//                         }
//                     }
//                 }
//
//                 var info = MemoryPackSerializer.Deserialize<MapInfo>(MapAsset.bytes);
//                 _map = new Map(Grid, tiles, sprites, info);
//                 _cells = new bool[_map.Cells.GetLength(0), _map.Cells.GetLength(1)];
//             }
//             catch (Exception e)
//             {
//                 Debug.LogError(e.Message);
//             }
//         }
//
//         [ShowIf("_map")]
//         [BoxGroup("Map Info")]
//         [Button("Load All Cells", ButtonSizes.Large)]
//         [GUIColor(0, 0.9f, 0)]
//         public void LoadAllCells()
//         {
//             for (var y = 0; y < _cells.GetLength(1); y++)
//             for (var x = 0; x < _cells.GetLength(0); x++)
//                 _cells[x, y] = true;
//
//             OnCellsChanged();
//         }
//
//         [ShowIf("_map")]
//         [BoxGroup("Map Info")]
//         [Button("Unload All Cells", ButtonSizes.Large)]
//         [GUIColor(0.9f, 0, 0)]
//         public void UnloadAllCells()
//         {
//             for (var y = 0; y < _cells.GetLength(1); y++)
//             for (var x = 0; x < _cells.GetLength(0); x++)
//                 _cells[x, y] = false;
//
//             OnCellsChanged();
//         }
//
//         private void OnCellsChanged()
//         {
//             if (_map == null)
//                 return;
//
//             for (var y = 0; y < _cells.GetLength(1); y++)
//             for (var x = 0; x < _cells.GetLength(0); x++)
//             {
//                 if (_map.Cells[x, y] != null == _cells[x, y])
//                     continue;
//
//                 if (_cells[x, y])
//                     _map.LoadCell(x, y);
//                 else
//                     _map.UnloadCell(x, y);
//             }
//
//             SceneView.RepaintAll();
//         }
//
// #if UNITY_EDITOR
//         private static bool DrawCellElement(Rect rect, bool value)
//         {
//             if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
//             {
//                 value = !value;
//                 GUI.changed = true;
//                 Event.current.Use();
//             }
//
//             EditorGUI.DrawRect(rect.Padding(1), value ? new Color(0.1f, 0.8f, 0.2f) : new Color(0, 0, 0, 0.5f));
//
//             return value;
//         }
// #endif // UNITY_EDITOR
//     }
// }