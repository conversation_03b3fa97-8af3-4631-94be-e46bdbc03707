﻿using System;
using System.Collections.Generic;
using R3;
using UnityEngine;

namespace Universe
{
    public abstract class UIElement : IUIElement
    {
        protected static Transform _cacheTransform;

        static UIElement()
        {
            var go = GameObject.FindWithTag("UICache");
            go.SetActive(false);
            _cacheTransform = go.transform;
        }

        protected readonly string _name;
        protected object _userdata;
        protected BehaviorSubject<int> _stateSubject;
        protected CompositeDisposable _disposables;
        protected Transform _transform;
        protected IUIElement _parent;
        protected List<IUIElement> _children;

        protected IMediator _mediator;

        public string Name => _name;

        public object Userdata => _userdata;

        public bool IsDisposed { get; private set; }

        public BehaviorSubject<int> StateSubject
        {
            get
            {
                if (_stateSubject == null)
                    _stateSubject = new BehaviorSubject<int>(0);
                return _stateSubject;
            }
        }

        public virtual ICollection<IDisposable> Disposables
        {
            get
            {
                if (_disposables == null)
                    _disposables = new CompositeDisposable();
                return _disposables;
            }
        }

        public Transform Transform => _transform;

        public virtual IUIElement Parent => _parent;

        public List<IUIElement> Children
        {
            get
            {
                if (_children == null)
                    _children = new List<IUIElement>();

                return _children;
            }
        }

        public UIElement(string name, object userdata)
        {
            _name = name;
            _userdata = userdata;
        }

        public abstract void SetParent(IUIElement parent, Transform parentTransform = null);

        public virtual void Dispose()
        {
            if (IsDisposed)
                return;

            try
            {
                Dispose(true);
            }
            finally
            {
                IsDisposed = true;
            }
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposing)
                return;

            if (_children != null)
            {
                for (var i = _children.Count - 1; i >= 0; i--)
                    _children[i].Dispose();
                _children.Clear();
                _children = null;
            }

            _stateSubject?.Dispose();
            _disposables?.Dispose();

            if (_parent != null)
                SetParent(null);

            _mediator?.Dispose();
            _transform = null;
        }
    }
}