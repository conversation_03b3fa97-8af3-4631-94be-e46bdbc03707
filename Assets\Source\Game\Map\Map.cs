﻿using System;
using System.Collections.Generic;
using MemoryPack;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Profiling;
using UnityEngine.ResourceManagement.AsyncOperations;

namespace CLMM
{
    public class Map
    {
        public int Id { get; }
        public int ChunkCountX { get; }
        public int ChunkCountY { get; }

        private MapInfo _mapInfo;
        private ChunkInfo[,] _chunkInfos;

        private readonly Transform _transform;

        private Vector2Int _chunkAoiRadius;
        private Vector2Int _chunkAoiIndex;
        // private Chunk _currentChunk;
        private readonly Dictionary<Vector2Int, Chunk> _activeChunks = new();
        private readonly List<Chunk> _removeChunks = new();

        // TODO: Pool<Chunk> _freeChunks
        // private List<Chunk> _freeChunks = new();

        public Map(string name)
        {
            Profiler.BeginSample("Load MapInfo");
            var handle = Addressables.LoadAssetAsync<TextAsset>($"Map/Data/{name}.map");
            handle.WaitForCompletion();
            if (handle.Status != AsyncOperationStatus.Succeeded)
                throw new Exception("Load Map Data Failed");
            _mapInfo = MemoryPackSerializer.Deserialize<MapInfo>(handle.Result.bytes);
            Addressables.Release(handle);
            Profiler.EndSample();

            ChunkCountX = (int)((float)_mapInfo.Width / World.ChunkSize + 0.5f);
            ChunkCountY = (int)((float)_mapInfo.Height / World.ChunkSize + 0.5f);

            _chunkInfos = new ChunkInfo[ChunkCountX, ChunkCountY];
        }

        public void Update()
        {
            if (World.PlayerController == null)
                return;

            // 通过PlayerController.Target的位置计算当前的Chunk
            var target = World.PlayerController.Target;
            var chunkAoiIndex = new Vector2Int(
                target.Location.x / World.ChunkSize,
                target.Location.y / World.ChunkSize
            );

            var camera = World.Camera;
            var orthographicSize = camera.orthographicSize;
            var sizeX = (int)(orthographicSize * Screen.width / Screen.height * 2f / World.TileSize.x / World.ChunkSize / 2 + 1f);
            var sizeY = (int)(orthographicSize * 2f / World.TileSize.y / World.ChunkSize / 2 + 1f);
            var size = Math.Max(sizeX, sizeY);
            var chunkAoiRadius = new Vector2Int(size, size);

            // 如果当前Chunk没有改变,则返回
            if (_chunkAoiRadius == chunkAoiRadius && _chunkAoiIndex == chunkAoiIndex)
                return;

            // 清理离开边界值的_activeChunks
            foreach (var chunk in _activeChunks.Values)
            {
                if (chunkAoiIndex.x - chunkAoiRadius.x - World.ChunkCacheDistance > chunk.Index.x
                    || chunkAoiIndex.x + chunkAoiRadius.x + World.ChunkCacheDistance < chunk.Index.x
                    || chunkAoiIndex.y - chunkAoiRadius.y - World.ChunkCacheDistance > chunk.Index.y
                    || chunkAoiIndex.y + chunkAoiRadius.y + World.ChunkCacheDistance < chunk.Index.y)
                {
                    chunk.Unload();
                    _removeChunks.Add(chunk);
                    // TODO: Pool<Chunk> _freeChunks
                    // _freeChunks.Add(Chunk);
                }
            }

            foreach (var chunk in _removeChunks)
            {
                _activeChunks.Remove(chunk.Index);
                chunk.Dispose();
            }
            _removeChunks.Clear();

            // 添加进入边界的_activeChunks
            for (var y = chunkAoiIndex.y - chunkAoiRadius.y; y <= chunkAoiIndex.y + chunkAoiRadius.y; y++)
            {
                for (var x = chunkAoiIndex.x - chunkAoiRadius.x; x <= chunkAoiIndex.x + chunkAoiRadius.x; x++)
                {
                    if (x < 0 || x >= ChunkCountX || y < 0 || y >= ChunkCountY)
                        continue;

                    var index = new Vector2Int(x, y);
                    if (_activeChunks.ContainsKey(index))
                        continue;

                    // TODO: Pool<Chunk> _freeChunks
                    var chunk = new Chunk();
                    var chunkInfo = _chunkInfos[index.x, index.y];
                    if (chunkInfo == null)
                    {
                        Profiler.BeginSample("Load ChunkInfo");
                        chunkInfo = MemoryPackSerializer.Deserialize<ChunkInfo>(_mapInfo.Chunks[index.x, index.y]);
                        _chunkInfos[index.x, index.y] = chunkInfo;
                        Profiler.EndSample();
                    }
                    chunk.Load(chunkInfo);
                    _activeChunks.Add(index, chunk);
                }
            }

            _chunkAoiIndex = chunkAoiIndex;
            _chunkAoiRadius = chunkAoiRadius;
        }
    }
}