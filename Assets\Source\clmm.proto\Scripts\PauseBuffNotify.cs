﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class PauseBuffNotify : IMessage
    {
        public int Id => (ushort)MessageId.PauseBuffNotify;
    }

    [Packable(Id = (int)MessageId.PauseBuffNotify)]
    public class PauseBuffNotifyPackable : MessagePackable<PauseBuffNotify>
    {
        protected override void Serialize(IByteBuffer buf, PauseBuffNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out PauseBuffNotify message)
        {
            message = new PauseBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}