﻿using System;
using System.Text;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class LoginRequest : IMessage
    {
        public int Id => (ushort)MessageId.LoginRequest;

        public string Account;
        public string Password;
    }

    [Packable(Type = typeof(LoginRequest))]
    public class LoginRequestPackable : MessagePackable<LoginRequest>
    {
        protected override void Serialize(IByteBuffer buf, LoginRequest packet)
        {
            buf.WriteFixedString(packet.Account, Encoding.UTF8);
            buf.WriteFixedString(packet.Password, Encoding.UTF8);
        }

        protected override void Deserialize(IByteB<PERSON>er buf, out LoginRequest message)
        {
            throw new NotSupportedException();
        }
    }
}