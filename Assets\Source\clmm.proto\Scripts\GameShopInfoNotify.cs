﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.DEBUG)]
    public class GameShopInfoNotify : IMessage
    {
        public int Id => (ushort)MessageId.GameShopInfoNotify;
    }

    [Packable(Id = (int)MessageId.GameShopInfoNotify)]
    public class GameShopInfoNotifyPackable : MessagePackable<GameShopInfoNotify>
    {
        protected override void Serialize(IByteBuffer buf, GameShopInfoNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out GameShopInfoNotify message)
        {
            message = new GameShopInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}