﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.DEBUG)]
    public class NewItemInfoNotify : IMessage
    {
        public int Id => (ushort)MessageId.NewItemInfoNotify;
    }

    [Packable(Id = (int)MessageId.NewItemInfoNotify)]
    public class NewItemInfoNotifyPackable : MessagePackable<NewItemInfoNotify>
    {
        protected override void Serialize(IByteBuffer buf, NewItemInfoNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out NewItemInfoNotify message)
        {
            message = new NewItemInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}