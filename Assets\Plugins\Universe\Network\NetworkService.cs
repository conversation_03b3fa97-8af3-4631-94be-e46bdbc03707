﻿using System;
using System.Threading.Tasks;
using DotNetty.Common.Internal.Logging;
using DotNetty.Transport.Channels;
using NLog.Extensions.Logging;
using slf4net;

namespace Universe
{
    public class NetworkService : IService
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(NetworkService));

        private readonly IMessageDispatcher _dispatcher;

        static NetworkService()
        {
            InternalLoggerFactory.DefaultFactory = new NLogLoggerFactory();
        }

        public NetworkService(IMessageDispatcher dispatcher)
        {
            _dispatcher = dispatcher;
        }

        public IEventLoopGroup EventLoopGroup { get; private set; }

        public void Initialize()
        {
            EventLoopGroup = new SingleThreadEventLoop();
        }

        public void Update()
        {
            _dispatcher.Execute();
        }

        public void Shutdown()
        {
#if UNITY_EDITOR
            EventLoopGroup.ShutdownGracefullyAsync(
                    TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1))
                .ContinueWith(task =>
                {
                    if (Logger.IsErrorEnabled)
                        Logger.Error($"{nameof(Shutdown)}: Error shutdown event loop group.\r\nException - {task.Exception}");
                }, TaskContinuationOptions.OnlyOnFaulted);
#endif // UNITY_EDITOR
        }
    }
}