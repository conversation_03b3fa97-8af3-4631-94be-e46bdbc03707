﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class LoverUpdateNotify : IMessage
    {
        public int Id => (ushort)MessageId.LoverUpdateNotify;
    }

    [Packable(Id = (int)MessageId.LoverUpdateNotify)]
    public class LoverUpdateNotifyPackable : MessagePackable<LoverUpdateNotify>
    {
        protected override void Serialize(IByteBuffer buf, LoverUpdateNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out LoverUpdateNotify message)
        {
            message = new LoverUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}