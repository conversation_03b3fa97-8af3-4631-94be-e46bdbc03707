﻿using Autofac;
using CLMM.Constant;
using slf4net;
using Universe;
using ILogger = slf4net.ILogger;

namespace CLMM.Scene
{
    [Component(typeof(IScene), "WorldScene")]
    public class WorldScene : BaseScene
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(WorldScene));

        public override int Tag => (int)GroupId.World;
        protected override string[] LoadUIViews => new[] { "WorldUI" };
        protected override string[] UnloadUIViews => new[] { "WorldUI" };

        public WorldScene(ILifetimeScope container) : base(container) { }

        public override void Activate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Activating.");

            base.Activate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Activated.");
        }

        public override void Deactivate()
        {
            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivating.");

            base.Deactivate();

            if (Logger.IsInfoEnabled)
                Logger.Info("Deactivated.");
        }
    }
}