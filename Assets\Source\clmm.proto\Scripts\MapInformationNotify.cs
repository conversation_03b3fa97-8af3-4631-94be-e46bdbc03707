﻿using System;
using System.Text;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class MapInformationNotify : IMessage
    {
        public int Id => (ushort)MessageId.MapInformationNotify;

        public int MapIndex;
        public string FileName;
        public string Title;
        public ushort MiniMap, BigMap;
        public byte Lights;
        public bool Lightning, Fire;
        public byte MapDarkLight;
        public ushort Music;
        public ushort WeatherSetting;
    }

    [Packable(Id = (int)MessageId.MapInformationNotify)]
    public class MapInformationNotifyPackable : MessagePackable<MapInformationNotify>
    {
        protected override void Serialize(IByteBuffer buf, MapInformationNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out MapInformationNotify message)
        {
            message = new MapInformationNotify();
            message.MapIndex = buf.ReadIntLE();
            message.FileName = buf.ReadFixedString(Encoding.UTF8);
            message.Title = buf.ReadFixedString(Encoding.UTF8);
            message.MiniMap = buf.ReadUnsignedShortLE();
            message.BigMap = buf.ReadUnsignedShortLE();
            message.Lights = buf.ReadByte();
            var lightFlagBit = buf.ReadByte();
            if ((lightFlagBit & 0x01) != 0)
                message.Lightning = true;
            if ((lightFlagBit & 0x02) != 0)
                message.Fire = true;
            message.MapDarkLight = buf.ReadByte();
            message.Music = buf.ReadUnsignedShortLE();
            message.WeatherSetting = buf.ReadUnsignedShortLE();
        }
    }
}