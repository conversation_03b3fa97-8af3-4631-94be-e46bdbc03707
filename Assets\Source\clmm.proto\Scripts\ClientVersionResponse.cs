﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ClientVersionResponse : IMessage
    {
        public int Id => (ushort)MessageId.ClientVersionResponse;

        public byte Result;
    }

    [Packable(Id = (int)MessageId.ClientVersionResponse)]
    public class ClientVersionResponsePackable : MessagePackable<ClientVersionResponse>
    {
        protected override void Serialize(IByteBuffer buf, ClientVersionResponse packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ClientVersionResponse message)
        {
            message = new ClientVersionResponse();
            message.Result = buf.ReadByte();
        }
    }
}