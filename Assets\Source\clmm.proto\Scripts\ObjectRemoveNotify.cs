﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectRemoveNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectRemoveNotify;
    }

    [Packable(Id = (int)MessageId.ObjectRemoveNotify)]
    public class ObjectRemoveNotifyPackable : MessagePackable<ObjectRemoveNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectRemoveNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectRemoveNotify message)
        {
            message = new ObjectRemoveNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}