﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectPlayerNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectPlayerNotify;
    }

    [Packable(Id = (int)MessageId.ObjectPlayerNotify)]
    public class ObjectPlayerNotifyPackable : MessagePackable<ObjectPlayerNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectPlayerNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectPlayerNotify message)
        {
            message = new ObjectPlayerNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}