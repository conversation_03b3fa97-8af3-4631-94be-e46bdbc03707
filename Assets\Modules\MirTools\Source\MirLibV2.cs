﻿using System;
using System.IO;
using UnityEngine;

namespace CLMM.MirTools
{
    public class MirLibV2 : IDisposable
    {
        private readonly int[] _imageOffsets;
        private readonly BinaryReader _reader;
        private readonly FileStream _stream;

        public MirLibV2(string filename)
        {
            Name = Path.GetFileNameWithoutExtension(filename);
            _stream = new FileStream(filename, FileMode.Open, FileAccess.Read);
            _reader = new BinaryReader(_stream);

            var version = _reader.ReadInt32();
            if (version < 2)
            {
                Debug.LogError($"{filename} Wrong version, expecting lib version: 3 found version: {version}");
                return;
            }

            var imageCount = _reader.ReadInt32();

            var frameOffset = 0;
            if (version >= 3)
                frameOffset = _reader.ReadInt32();

            Images = new MirImage[imageCount];
            _imageOffsets = new int[imageCount];

            for (var i = 0; i < imageCount; i++)
                _imageOffsets[i] = _reader.ReadInt32();

            // for (var i = 0; i < imageCount; i++)
            // {
            //     var offset = _imageOffsets[i];
            //     _stream.Seek(offset, SeekOrigin.Begin);
            //     var image = new MirImage(_reader);
            //     Images.Add(image);
            //
            //     if (image.Width == 0 || image.Height == 0)
            //         continue;
            //
            //     _stream.Seek(offset + 17, SeekOrigin.Begin);
            //     image.LoadTexture(_reader);
            // }

            _stream.Seek(frameOffset, SeekOrigin.Begin);
            var frameCount = _reader.ReadInt32();
            if (frameCount > 0)
            {
                // throw new NotImplementedException("frameCount > 0");
            }
        }

        public string Name { get; }
        public MirImage[] Images { get; }

        public void Dispose()
        {
            _reader.Dispose();
            _stream.Dispose();
        }

        public MirImage LoadImage(int index)
        {
            if (index >= Images.Length)
                return null;

            if (Images[index] != null)
                return Images[index];

            var offset = _imageOffsets[index];
            _stream.Seek(offset, SeekOrigin.Begin);
            var image = new MirImage(_reader);
            Images[index] = image;
            _stream.Seek(offset + 17, SeekOrigin.Begin);
            image.LoadTexture(_reader);
            return image;
        }
    }
}