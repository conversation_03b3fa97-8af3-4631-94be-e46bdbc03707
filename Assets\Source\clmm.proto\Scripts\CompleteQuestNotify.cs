﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class CompleteQuestNotify : IMessage
    {
        public int Id => (ushort)MessageId.CompleteQuestNotify;
    }

    [Packable(Id = (int)MessageId.CompleteQuestNotify)]
    public class CompleteQuestNotifyPackable : MessagePackable<CompleteQuestNotify>
    {
        protected override void Serialize(IByteBuffer buf, CompleteQuestNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out CompleteQuestNotify message)
        {
            message = new CompleteQuestNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}