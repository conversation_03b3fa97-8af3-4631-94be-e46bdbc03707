using CLMM.Constant;
using CLMM.Packet;
using slf4net;
using Universe;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.KeepAliveResponse, Tag = (int)GroupId.Global)]
    public class KeepAliveResponseHandler : MessageHandler<KeepAliveResponse>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(KeepAliveResponseHandler));

        protected override void Handle(ISession session, KeepAliveResponse message)
        {
            if (Logger.IsTraceEnabled)
                Logger.Trace($"{nameof(Handle)}: Info - {nameof(KeepAliveResponse)} {JsonConvert.SerializeObject(message)}");
        }
    }
}