using System.Collections.Generic;
using System.IO;
using System.Linq;
using Sirenix.Utilities;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;

namespace CLMM.MirTools
{
    public partial class MirExport
    {
        public class FrameSet : SortedDictionary<int, Frame> { }

        public struct Frame
        {
            public int Orientation;
            public int Index;
            public int Count;
            public int Skip;
            public int Interval;
        }

        private static readonly FrameSet Default = new()
        {
            { 0, new Frame { Orientation = 8, Index = 0, Count = 4, Skip = 0, Interval = 500 } },
            { 1, new Frame { Orientation = 8, Index = 32, Count = 6, Skip = 0, Interval = 100 } },
            { 9, new Frame { Orientation = 8, Index = 80, Count = 6, Skip = 0, Interval = 100 } }
        };

        [MenuItem("MitTools/Export All Actor Animators")]
        public static void ExportAllActorAnimators()
        {
            EditorUtility.DisplayProgressBar("Exporting All Actor Animators", "Processing ...", 0);

            var mapping = new Dictionary<string, (string, FrameSet)>
            {
                { "Actor/Atlas", ("Actor/Animator", Default) }
            };

            try
            {
                foreach (var (key, (path, frameSet)) in mapping)
                {
                    var guids = AssetDatabase.FindAssets("t:SpriteAtlas", new[] { $"Assets/Content/{key}" });

                    var index = 0;
                    var count = guids.Length;
                    foreach (var guid in guids)
                    {
                        var texturePath = AssetDatabase.GUIDToAssetPath(guid);

                        EditorUtility.DisplayProgressBar(
                            $"Generating SpriteAtlas({++index}/{count})",
                            "Processing " + texturePath,
                            (float)index / count);

                        var animatorPath = texturePath.Replace(key, path)
                            .Replace(Path.GetExtension(texturePath), "");

                        ExportActorAnimator(texturePath, animatorPath, frameSet);
                    }
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                Resources.UnloadUnusedAssets();
            }
        }

        private static void ExportActorAnimator(string input, string output, FrameSet frameSet)
        {
            var spriteDict = new Dictionary<int, Sprite>();
            var spriteAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(input);
            var packables = spriteAtlas.GetPackables();
            foreach (var packable in packables)
            {
                if (packable is Texture2D texture)
                {
                    var texturePath = AssetDatabase.GetAssetPath(texture);
                    AssetDatabase.LoadAllAssetsAtPath(texturePath)
                        .OfType<Sprite>()
                        .ForEach(sprite => { spriteDict.Add(int.Parse(sprite.name), sprite); });
                }
            }

            var spriteAnimatorController = ScriptableObject.CreateInstance<SpriteAnimatorController>();

            foreach (var (id, frame) in frameSet)
            {
                var sequences = new SpriteAnimatorClip.Sequence[frame.Orientation];
                for (var o = 0; o < frame.Orientation; o++)
                {
                    var sprites = new Sprite[frame.Count];
                    for (var i = 0; i < frame.Count; i++)
                    {
                        var index = i + frame.Index + o * (frame.Count + frame.Skip);
                        if (!spriteDict.TryGetValue(index, out var sprite))
                            continue;
                        sprites[i] = sprite;
                    }

                    var sequence = new SpriteAnimatorClip.Sequence();
                    sequence.Sprites = sprites;

                    sequences[o] = sequence;
                }

                var spriteAnimatorClip = ScriptableObject.CreateInstance<SpriteAnimatorClip>();
                spriteAnimatorClip.Id = id;
                spriteAnimatorClip.FrameInterval = frame.Interval;
                spriteAnimatorClip.Sequences = sequences;

                var clipAssetPath = $"{output}/{id}.spriteanimatorclip.asset";
                var clipDirPath = Path.GetDirectoryName(clipAssetPath);
                if (!string.IsNullOrEmpty(clipDirPath) && !Directory.Exists(clipDirPath))
                    Directory.CreateDirectory(clipDirPath);

                AssetDatabase.CreateAsset(spriteAnimatorClip, clipAssetPath);

                spriteAnimatorController.Clips.Add(spriteAnimatorClip.Id, spriteAnimatorClip);
            }

            var controllerAssetPath = $"{output}.spriteanimatorcontroller.asset";
            var controllerDirPath = Path.GetDirectoryName(controllerAssetPath);
            if (!string.IsNullOrEmpty(controllerDirPath) && !Directory.Exists(controllerDirPath))
                Directory.CreateDirectory(controllerDirPath);

            AssetDatabase.CreateAsset(spriteAnimatorController, controllerAssetPath);

            Resources.UnloadUnusedAssets();
        }
    }
}