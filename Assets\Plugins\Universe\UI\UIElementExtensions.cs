﻿using System;
using R3;
using Unity.VisualScripting;
using UnityEngine;

namespace Universe
{
    public static class UIElementExtensions
    {
        public static void Observe<T>(this Observable<T> observable, IUIElement ui, Action<T> onNext)
        {
            observable.CombineLatest(ui.StateSubject, (value, state) => (value, state))
                .Where(result => result.state > 0)
                .Subscribe(result => onNext(result.value))
                .AddTo(ui.Disposables);
        }

        public static T AsLife<T>(this T disposable, IUIElement ui) where T : IDisposable
        {
            ui.Disposables.Add(disposable);
            return disposable;
        }

        public static T GetComponent<T>(this IUIElement ui) where T : Component
        {
            return ui.Transform.GetComponent<T>();
        }

        public static VariableDeclarations GetVariables(this IUIElement ui)
        {
            return Variables.Object(ui.Transform);
        }
    }
}