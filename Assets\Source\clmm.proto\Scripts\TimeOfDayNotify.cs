﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class TimeOfDayNotify : IMessage
    {
        public int Id => (ushort)MessageId.TimeOfDayNotify;
    }

    [Packable(Id = (int)MessageId.TimeOfDayNotify)]
    public class TimeOfDayNotifyPackable : MessagePackable<TimeOfDayNotify>
    {
        protected override void Serialize(IByteBuffer buf, TimeOfDayNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out TimeOfDayNotify message)
        {
            message = new TimeOfDayNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}