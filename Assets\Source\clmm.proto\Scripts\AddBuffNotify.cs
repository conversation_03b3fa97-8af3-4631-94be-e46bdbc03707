﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class AddBuffNotify : IMessage
    {
        public int Id => (ushort)MessageId.AddBuffNotify;
    }

    [Packable(Id = (int)MessageId.AddBuffNotify)]
    public class AddBuffNotifyPackable : MessagePackable<AddBuffNotify>
    {
        protected override void Serialize(IByteBuffer buf, AddBuffNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out AddBuffNotify message)
        {
            message = new AddBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}