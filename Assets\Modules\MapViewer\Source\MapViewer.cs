﻿using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Tilemaps;

namespace CLMM.MapViewer
{
    public class MapViewer : MonoBehaviour
    {
        private async void Start()
        {
            var initializeHandle = Addressables.InitializeAsync();
            await initializeHandle.Task;

            var handle = Addressables.LoadAssetAsync<GameObject>("Map/Prefab/Grid");
            var prefab = await handle.Task;
            var go = Instantiate(prefab);
            Addressables.Release(handle);
            go.name = "Grid";
            World.Grid = go.GetComponent<Grid>();
            World.SpritePrefab = go.transform.Find("FreeSprites/Sprite").gameObject;
            World.Tilemap = go.transform.Find("Base_Grid/Base_Layer").GetComponent<Tilemap>();
            World.Tilemaps[0] = go.transform.Find("0_Layer").GetComponent<Tilemap>();
            World.Tilemaps[1] = go.transform.Find("1_Layer").GetComponent<Tilemap>();
            World.ObjectTransform = go.transform.Find("Object_Layer");

            World.Camera = GameObject.FindWithTag("MainCamera").GetComponent<Camera>();

            World.PlayerController = new PlayerController();
            var target = new Actor();
            if (Camera.main != null)
            {
                var position = Camera.main.transform.position;
                target.Location = new Vector2Int((int)position.x, -(int)position.y);
            }
            World.PlayerController.Target = target;
            GetComponent<PlayerInputHandler>().Target = target;

            World.Map = new Map("0");
        }

        private void Update()
        {
            if (World.Map != null)
                World.Map.Update();
        }
    }
}