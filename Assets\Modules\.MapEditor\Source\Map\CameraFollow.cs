﻿using UnityEngine;

namespace CLMM
{
    public class CameraFollow : MonoBehaviour
    {
        public Transform Target; // 要追踪的目标
        public float SmoothTime = 0.125f;
        public Vector3 Offset; // 摄像机与目标的偏移量

        private Vector3 _velocity;

        private void LateUpdate()
        {
            transform.position = Vector3.SmoothDamp(
                transform.position,
                Target.position + Offset,
                ref _velocity,
                SmoothTime);
        }
    }
}