﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ReceiveMailNotify : IMessage
    {
        public int Id => (ushort)MessageId.ReceiveMailNotify;
    }

    [Packable(Id = (int)MessageId.ReceiveMailNotify)]
    public class ReceiveMailNotifyPackable : MessagePackable<ReceiveMailNotify>
    {
        protected override void Serialize(IByteBuffer buf, ReceiveMailNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ReceiveMailNotify message)
        {
            message = new ReceiveMailNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}