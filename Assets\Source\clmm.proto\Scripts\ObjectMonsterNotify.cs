﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class ObjectMonsterNotify : IMessage
    {
        public int Id => (ushort)MessageId.ObjectMonsterNotify;
    }

    [Packable(Id = (int)MessageId.ObjectMonsterNotify)]
    public class ObjectMonsterNotifyPackable : MessagePackable<ObjectMonsterNotify>
    {
        protected override void Serialize(IByteBuffer buf, ObjectMonsterNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out ObjectMonsterNotify message)
        {
            message = new ObjectMonsterNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}