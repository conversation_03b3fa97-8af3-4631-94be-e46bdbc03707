﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class MentorUpdateNotify : IMessage
    {
        public int Id => (ushort)MessageId.MentorUpdateNotify;
    }

    [Packable(Id = (int)MessageId.MentorUpdateNotify)]
    public class MentorUpdateNotifyPackable : MessagePackable<MentorUpdateNotify>
    {
        protected override void Serialize(IByteBuffer buf, MentorUpdateNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out MentorUpdateNotify message)
        {
            message = new MentorUpdateNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}