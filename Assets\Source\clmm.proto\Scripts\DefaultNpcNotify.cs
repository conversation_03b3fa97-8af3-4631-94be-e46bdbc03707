﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class DefaultNpcNotify : IMessage
    {
        public int Id => (ushort)MessageId.DefaultNpcNotify;
    }

    [Packable(Id = (int)MessageId.DefaultNpcNotify)]
    public class DefaultNpcNotifyPackable : MessagePackable<DefaultNpcNotify>
    {
        protected override void Serialize(IByteBuffer buf, DefaultNpcNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out DefaultNpcNotify message)
        {
            message = new DefaultNpcNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}