﻿using System;
using System.Reflection;
using Autofac;
using Universe;
using Module = Autofac.Module;

namespace CLMM
{
    public class GameModule : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            var types = Assembly.GetExecutingAssembly().GetTypes();
            foreach (var type in types)
            {
                if (Attribute.GetCustomAttribute(type,
                        typeof(ComponentAttribute)) is not ComponentAttribute attribute)
                    continue;

                var registrationBuilder = builder.RegisterType(type);
                if (attribute.Name is not null)
                    registrationBuilder.Named(attribute.Name, attribute.Service != null ? attribute.Service : type);
                if (attribute.Service is not null)
                    registrationBuilder.As(attribute.Service);
                if (attribute.Scope == Scope.InstancePerDependency)
                    registrationBuilder.InstancePerDependency();
                else if (attribute.Scope == Scope.SingleInstance)
                    registrationBuilder.SingleInstance();
                else
                    registrationBuilder.InstancePerLifetimeScope();
            }

            builder.RegisterType<PlayerController>().InstancePerLifetimeScope();
        }
    }
}