﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class RemoveBuffNotify : IMessage
    {
        public int Id => (ushort)MessageId.RemoveBuffNotify;
    }

    [Packable(Id = (int)MessageId.RemoveBuffNotify)]
    public class RemoveBuffNotifyPackable : MessagePackable<RemoveBuffNotify>
    {
        protected override void Serialize(IByteBuffer buf, RemoveBuffNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out RemoveBuffNotify message)
        {
            message = new RemoveBuffNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}