﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    [Protocol(LogLevel.DEBUG)]
    public class NewQuestInfoNotify : IMessage
    {
        public int Id => (ushort)MessageId.NewQuestInfoNotify;
    }

    [Packable(Id = (int)MessageId.NewQuestInfoNotify)]
    public class NewQuestInfoNotifyPackable : MessagePackable<NewQuestInfoNotify>
    {
        protected override void Serialize(IByteBuffer buf, NewQuestInfoNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out NewQuestInfoNotify message)
        {
            message = new NewQuestInfoNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}