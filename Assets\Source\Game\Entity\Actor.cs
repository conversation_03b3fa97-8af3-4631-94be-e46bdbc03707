﻿using UnityEngine;

namespace CLMM
{
    public class Actor
    {
        private Vector2Int _location;
        public Vector2Int Location
        {
            get => _location;
            set
            {
                _location = value;
                Transform.position = new Vector3(
                    _location.x * World.TileSize.x,
                    -_location.y * World.TileSize.y,
                    0);
            }
        }

        public Transform Transform { get; set; }
    }
}