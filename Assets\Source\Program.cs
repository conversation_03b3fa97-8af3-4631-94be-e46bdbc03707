﻿using Autofac;
using UnityEngine;
using UnityEngine.SceneManagement;
using Universe;

namespace CLMM
{
    internal static class Program
    {
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
        private static void Main()
        {
            var scene = SceneManager.GetActiveScene();
            if (scene.name != "Game")
                return;

            var builder = new ContainerBuilder();
            builder.RegisterModule<LoggingModule>();
            builder.RegisterModule<EventModule>();
            builder.RegisterModule<ResourceModule>();
            builder.RegisterModule<NetworkModule>();
            builder.RegisterModule<UIModule>();
            builder.RegisterModule<GameModule>();
            var container = builder.Build();
            GameBehavior.Instance.Initialize(container);
        }
    }
}