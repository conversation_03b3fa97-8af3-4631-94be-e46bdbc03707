using CLMM.Constant;
using CLMM.Packet;
using CLMM.Repository;
using slf4net;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Tilemaps;
using Universe;
using ILogger = slf4net.ILogger;
using JsonConvert = Newtonsoft.Json.JsonConvert;

namespace CLMM.Network
{
    [Component]
    [Handler((int)MessageId.MapInformationNotify, Tag = (int)GroupId.World)]
    public class MapInformationNotifyHandler : MessageHandler<MapInformationNotify>
    {
        private static readonly ILogger Logger = LoggerFactory.GetLogger(typeof(MapInformationNotifyHandler));

        // private readonly MapRepository _mapRepository;
        //
        // public MapInformationNotifyHandler(MapRepository mapRepository)
        // {
        //     _mapRepository = mapRepository;
        // }

        protected override void Handle(ISession session, MapInformationNotify message)
        {
            if (Logger.IsDebugEnabled)
                Logger.Debug($"{nameof(Handle)}: Info - {message.GetType().Name} {JsonConvert.SerializeObject(message)}");

            // 更新地图信息到仓库
            // _mapRepository.UpdateMapInformation(message);

            var handle = Addressables.LoadAssetAsync<GameObject>("Map/Prefab/Grid");
            var prefab = handle.WaitForCompletion();
            var go = Object.Instantiate(prefab);
            Addressables.Release(handle);
            go.name = "Grid";
            World.Grid = go.GetComponent<Grid>();
            World.SpritePrefab = go.transform.Find("FreeSprites/Sprite").gameObject;
            World.Tilemap = go.transform.Find("Base_Grid/Base_Layer").GetComponent<Tilemap>();
            World.Tilemaps[0] = go.transform.Find("0_Layer").GetComponent<Tilemap>();
            World.Tilemaps[1] = go.transform.Find("1_Layer").GetComponent<Tilemap>();
            World.ObjectTransform = go.transform.Find("Object_Layer");

            World.Map = new Map(message.FileName);
        }
    }
}