﻿using System;
using System.Collections.Generic;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class LoginSuccessNotify : IMessage
    {
        public int Id => (ushort)MessageId.LoginSuccessNotify;

        public List<SelCharInfo> SelCharInfos = new(4);
    }

    [Packable(Id = (int)MessageId.LoginSuccessNotify)]
    public class LoginSuccessNotifyPackable : MessagePackable<LoginSuccessNotify>
    {
        protected override void Serialize(IByteBuffer buf, LoginSuccessNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out LoginSuccessNotify message)
        {
            message = new LoginSuccessNotify();
            var count = buf.ReadIntLE();
            message.SelCharInfos = new List<SelCharInfo>();
            for (var i = 0; i < count; i++)
            {
                var info = buf.ReadSelCharInfo();
                message.SelCharInfos.Add(info);
            }
        }
    }
}