﻿using System;
using DotNetty.Buffers;
using Universe;

namespace CLMM.Packet
{
    public class SwitchGroupNotify : IMessage
    {
        public int Id => (ushort)MessageId.SwitchGroupNotify;
    }

    [Packable(Id = (int)MessageId.SwitchGroupNotify)]
    public class SwitchGroupNotifyPackable : MessagePackable<SwitchGroupNotify>
    {
        protected override void Serialize(IByteBuffer buf, SwitchGroupNotify packet)
        {
            throw new NotSupportedException();
        }

        protected override void Deserialize(IByteBuffer buf, out SwitchGroupNotify message)
        {
            message = new SwitchGroupNotify();
            buf.SkipBytes(buf.ReadableBytes);
        }
    }
}