﻿using System.Net;
using System.Threading.Tasks;
using Autofac;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;

namespace Universe
{
    public class Connector : IConnector
    {
        private readonly Bootstrap _bootstrap = new();

        public Connector(ILifetimeScope container, IProtocolSerializer serializer, IMessageDispatcher dispatcher, IMessageFilter filter)
        {
            _bootstrap
                .Group(container.Resolve<NetworkService>().EventLoopGroup)
                .Channel<TcpSession>()
                .Option(ChannelOption.TcpNodelay, true)
                .Handler(new ChannelInitializer(container, serializer, dispatcher, filter));
        }

        public Task<ISession> ConnectAsync(EndPoint address)
        {
            return _bootstrap.ConnectAsync(address)
                .ContinueWith(task =>
                {
                    if (task.IsFaulted)
                        throw task.Exception;
                    return (ISession)task.Result;
                });
        }
    }
}